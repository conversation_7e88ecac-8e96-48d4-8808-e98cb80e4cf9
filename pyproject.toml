[project]
name = "boce"
version = "0.1.0"
description = "帮买车页面自动化测试项目"
requires-python = ">=3.12"
authors = [
    { name = "Developer", email = "<EMAIL>" }
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "pytest>=8.0.0",
    "pytest-playwright>=0.4.0",
    "playwright>=1.40.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest-xdist",
    "pytest-html",
    "pytest-cov",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["cases"]

[tool.uv]
dev-dependencies = [
    "pytest>=8.0.0",
    "pytest-playwright>=0.4.0",
    "playwright>=1.40.0",
    "loguru>=0.7.0",
    "pytest-xdist",
    "pytest-html",
    "pytest-cov",
]

[tool.pytest.ini_options]
addopts = "-p pytest_playwright --tb=short -v"
python_files = ["test_*.py", "*_test.py", "cases/*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
testpaths = ["cases"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.coverage.run]
source = ["cases"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]

# 项目脚本可以通过uv run命令执行
# 例如: uv run pytest
# 例如: uv run pytest --headed
# 例如: uv run playwright install