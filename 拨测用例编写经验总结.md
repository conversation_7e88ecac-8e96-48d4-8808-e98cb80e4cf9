# 拨测用例编写经验总结

## 1. 拨测用例设计原则

### 1.1 通用性原则
- **避免硬编码具体内容**：不依赖特定品牌、产品名称、价格等易变数据
- **关注功能逻辑**：验证业务流程和交互逻辑的正确性
- **数据无关性**：测试用例应该在数据变化时仍能正常运行

### 1.2 稳定性原则
- **使用可靠的选择器**：优先使用 data-testid、id 等稳定属性
- **添加适当等待**：确保页面元素完全加载后再进行操作
- **容错处理**：考虑网络延迟、加载时间等不确定因素

### 1.3 可维护性原则
- **模块化设计**：将选择器、配置等抽取为类属性
- **清晰的测试结构**：每个测试方法职责单一，命名清晰
- **充分的注释**：说明测试目的和关键逻辑

## 2. 通用功能验证 vs 具体内容验证

### 2.1 通用功能验证（推荐）
```python
# ✅ 好的做法：验证列表不为空
def test_brand_list_not_empty(self, page):
    brand_items = page.locator(self.brand_list_selector)
    expect(brand_items).to_have_count_greater_than(0)

# ✅ 好的做法：验证筛选功能
def test_filter_functionality(self, page):
    # 点击筛选条件
    page.click("[data-filter='price']")
    # 验证URL参数变化
    expect(page).to_have_url(re.compile(r".*price=.*"))
```

### 2.2 具体内容验证（避免）
```python
# ❌ 不好的做法：依赖具体品牌
def test_bmw_exists(self, page):
    expect(page.locator("text=宝马")).to_be_visible()

# ❌ 不好的做法：依赖具体价格
def test_price_range(self, page):
    expect(page.locator("text=10-20万")).to_be_visible()
```

## 3. 选择器设计最佳实践

### 3.1 选择器优先级（基于Playwright推荐）
1. **data-testid**：专门为测试设计的属性（最推荐）
   ```python
   page.locator("[data-testid='car-card']")
   ```
2. **id**：唯一标识符
   ```python
   page.locator("#car-list")
   ```
3. **准确的xpath**：精确定位元素
   ```python
   page.locator("//div[@class='series_card']")
   ```
4. **CSS选择器**：使用准确的类名和属性
   ```python
   page.locator(".series_card")  # 准确的类名
   ```

### 3.2 选择器准确性要求
**❌ 避免使用模糊选择器：**
- `li` - 过于通用
- `.item` - 可能匹配多种元素
- `[class*='item']` - 部分匹配不够精确
- `[data-testid*='item']` - 部分匹配可能误选

**✅ 使用准确的选择器：**
- `//div[@class='series_card']` - 精确的xpath
- `[data-testid='car-card-123']` - 完整的测试ID
- `.series_card` - 准确的类名
- `#car-list-container` - 具体的ID

### 3.3 选择器示例和最佳实践
```python
class TestMall:
    # 使用类属性管理选择器 - 优先使用稳定属性
    brand_list_selector = "[data-testid='brand-list']"
    car_card_selector = "//div[@class='series_card']"  # 准确的xpath
    price_filter_selector = "[data-testid='price-filter']"
    tab_new_cars_selector = "[data-testid='tab-new-cars']"
    
    def test_elements_exist(self, page):
        # 使用稳定的选择器验证元素存在
        expect(page.locator(self.brand_list_selector)).to_be_visible()
        expect(page.locator(self.car_card_selector)).to_have_count_greater_than(0)
        
    def test_accurate_selectors(self, page):
        # ✅ 好的做法：使用准确的选择器
        car_cards = page.locator("//div[@class='series_card']")
        expect(car_cards).to_have_count_greater_than(0)
        
        # ❌ 避免的做法：模糊选择器
        # page.locator("li")  # 太通用
        # page.locator(".item")  # 可能匹配错误元素
        # page.locator("[class*='item']")  # 部分匹配不精确
```

## 4. 测试用例结构和组织

### 4.1 标准测试类结构
```python
class TestMall:
    """车商城页面拨测用例"""
    
    # 配置常量
    BASE_URL = "https://example.com/mall"
    
    # 选择器定义
    brand_list_selector = "..."
    
    @pytest.fixture(autouse=True)
    def setup(self, page):
        """测试前置条件"""
        page.goto(self.BASE_URL)
        page.wait_for_load_state("networkidle")
    
    def test_basic_elements(self, page):
        """基础元素验证"""
        pass
    
    def test_user_interactions(self, page):
        """用户交互测试"""
        pass
```

### 4.2 测试方法命名规范
- `test_basic_*`：基础功能测试
- `test_*_functionality`：功能性测试
- `test_*_interaction`：交互测试
- `test_*_performance`：性能测试

## 5. 常见拨测场景和处理方法

### 5.1 页面加载验证
```python
def test_page_loads_successfully(self, page):
    """验证页面成功加载"""
    # 等待关键元素出现
    page.wait_for_selector(self.main_content_selector, timeout=10000)
    # 验证页面标题
    expect(page).to_have_title(re.compile(r".*商城.*"))
```

### 5.2 列表数据验证
```python
def test_data_list_not_empty(self, page):
    """验证数据列表不为空"""
    items = page.locator(self.item_list_selector)
    expect(items).to_have_count_greater_than(0)
    
    # 验证每个项目包含必要信息
    first_item = items.first
    expect(first_item.locator(".title")).to_be_visible()
    expect(first_item.locator(".price")).to_be_visible()
```

### 5.3 筛选功能验证
```python
def test_filter_updates_url(self, page):
    """验证筛选功能更新URL参数"""
    # 记录初始URL
    initial_url = page.url
    
    # 点击筛选条件
    page.click(self.filter_selector)
    
    # 验证URL发生变化
    page.wait_for_url(lambda url: url != initial_url)
    expect(page).to_have_url(re.compile(r".*filter=.*"))
```

### 5.4 切换功能验证
```python
def test_tab_switching(self, page):
    """验证标签页切换功能"""
    # 点击第二个标签
    page.click("[data-tab='used-cars']")
    
    # 验证内容区域更新
    expect(page.locator("[data-content='used-cars']")).to_be_visible()
    expect(page.locator("[data-content='new-cars']")).to_be_hidden()
```

## 6. Playwright + Pytest 框架使用经验

### 6.1 配置文件设置（conftest.py）
```python
@pytest.fixture
def browser_context_args(browser_context_args):
    """移动端设备模拟配置"""
    return {
        **browser_context_args,
        **playwright.devices["iPhone 12 Pro"],
        "locale": "zh-CN",
        "timezone_id": "Asia/Shanghai"
    }

@pytest.fixture
def browser_type_launch_args(browser_type_launch_args):
    """浏览器启动参数"""
    return {
        **browser_type_launch_args,
        "headless": False,  # 有头模式便于调试
        "slow_mo": 500     # 减慢操作速度
    }
```

### 6.2 等待策略
```python
# 等待网络空闲
page.wait_for_load_state("networkidle")

# 等待特定元素
page.wait_for_selector("[data-testid='loading']", state="hidden")

# 等待URL变化
page.wait_for_url(re.compile(r".*filter=.*"))

# 自定义等待条件
page.wait_for_function("() => document.querySelectorAll('[data-testid*=\"card\"]').length > 0")
```

### 6.3 断言最佳实践
```python
# 使用 expect 进行断言
from playwright.sync_api import expect

# 元素可见性
expect(page.locator(".element")).to_be_visible()

# 元素数量
expect(page.locator(".items")).to_have_count_greater_than(0)

# 文本内容（使用正则表达式）
expect(page.locator(".title")).to_have_text(re.compile(r".*商城.*"))

# URL验证
expect(page).to_have_url(re.compile(r".*mall.*"))
```

## 7. 移动端测试注意事项

### 7.1 设备模拟配置
```python
# 使用预定义设备
playwright.devices["iPhone 12 Pro"]
playwright.devices["Samsung Galaxy S21"]

# 自定义设备配置
{
    "viewport": {"width": 375, "height": 812},
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)...",
    "device_scale_factor": 3,
    "is_mobile": True,
    "has_touch": True
}
```

### 7.2 移动端特有测试
```python
def test_mobile_responsive(self, page):
    """移动端响应式测试"""
    # 验证移动端布局
    expect(page.locator(".mobile-menu")).to_be_visible()
    expect(page.locator(".desktop-menu")).to_be_hidden()

def test_touch_interactions(self, page):
    """触摸交互测试"""
    # 模拟滑动操作
    page.locator("[data-testid='carousel']").swipe_left()
    
    # 模拟长按
    page.locator("//div[@class='series_card']").first.long_press()
```

## 8. 错误处理和调试技巧

### 8.1 异常处理
```python
def test_with_error_handling(self, page):
    """带错误处理的测试"""
    try:
        page.wait_for_selector(".element", timeout=5000)
    except TimeoutError:
        # 截图保存错误现场
        page.screenshot(path="error_screenshot.png")
        raise
```

### 8.2 调试技巧
```python
# 添加调试断点
page.pause()  # 暂停执行，打开调试器

# 输出页面信息
print(f"Current URL: {page.url}")
print(f"Page title: {page.title()}")

# 截图保存
page.screenshot(path="debug.png")

# 保存页面HTML
with open("debug.html", "w") as f:
    f.write(page.content())
```

### 8.3 日志记录
```python
import logging
from loguru import logger

def test_with_logging(self, page):
    """带日志记录的测试"""
    logger.info(f"开始测试页面: {page.url}")
    
    try:
        # 测试逻辑
        pass
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise
    finally:
        logger.info("测试完成")
```

## 9. 测试稳定性保证

### 9.1 重试机制
```python
@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_flaky_feature(self, page):
    """可能不稳定的测试，自动重试"""
    pass
```

### 9.2 环境隔离
```python
@pytest.fixture
def clean_environment(self, page):
    """清理测试环境"""
    # 清除缓存
    page.context.clear_cookies()
    page.context.clear_permissions()
    
    yield
    
    # 测试后清理
    page.context.clear_cookies()
```

### 9.3 数据准备
```python
@pytest.fixture
def test_data(self):
    """准备测试数据"""
    return {
        "valid_filters": ["price", "brand", "type"],
        "expected_elements": [".header", ".content", ".footer"]
    }
```

## 10. 实际案例分析：车商城拨测

### 10.1 需求分析
- **目标页面**：车商城列表页
- **核心功能**：品牌筛选、价格筛选、新车/二手车切换
- **测试重点**：功能完整性、数据展示、用户交互

### 10.2 测试策略
1. **基础验证**：页面加载、关键元素存在
2. **功能测试**：筛选、切换、搜索功能
3. **数据验证**：列表不为空、信息完整
4. **交互测试**：点击、滑动等用户操作
5. **性能测试**：加载时间、响应速度

### 10.3 关键改进点
```python
# 改进前：依赖具体内容
def test_bmw_brand_exists(self, page):
    expect(page.locator("text=宝马")).to_be_visible()

# 改进后：验证通用功能
def test_brand_list_functionality(self, page):
    brand_items = page.locator(self.brand_list_selector)
    expect(brand_items).to_have_count_greater_than(0)
    
    # 点击第一个品牌
    first_brand = brand_items.first
    first_brand.click()
    
    # 验证筛选生效（URL参数变化）
    expect(page).to_have_url(re.compile(r".*brand=.*"))
```

### 10.4 测试覆盖范围
- ✅ 页面基础功能（加载、导航）
- ✅ 数据展示（列表、详情）
- ✅ 用户交互（筛选、切换）
- ✅ 移动端适配（响应式、触摸）
- ✅ 性能表现（加载速度）
- ✅ 错误处理（网络异常、数据为空）

## 总结

拨测用例编写的核心是**通用性**和**稳定性**。通过关注功能逻辑而非具体内容，使用可靠的选择器和等待策略，配合完善的错误处理和调试机制，可以构建出高质量、易维护的自动化测试套件。

在实际项目中，应该根据业务特点调整测试策略，但始终坚持"测试功能，不测数据"的原则，确保测试用例的长期价值。