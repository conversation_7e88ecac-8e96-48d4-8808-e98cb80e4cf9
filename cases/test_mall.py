import pytest
from playwright.sync_api import Page, expect


@pytest.fixture(scope="function")
def browser_context_args(browser_context_args, playwright):
    """配置iPhone 12 Pro设备模拟"""
    iphone_12 = playwright.devices['iPhone 12 Pro']
    return {
        **browser_context_args,
        **iphone_12,
    }


class TestMall:
    """车商城页面通用功能测试"""

    # 页面URL和基本元素选择器
    base_url = "https://energyspace.m.autohome.com.cn/ahoh-marketing/mall"
    query_params = "cityid=810100"

    brand_section_selector = "//div[@class='brand_list']"  # 品牌区域
    brand_list_selector = "//div[@class='brand_item']"  # 品牌列表

    # 车型分类选择器（优先使用稳定属性）
    new_car_tab_selector = "//div[@class='tab_bar']/div[1]"
    old_car_tab_selector = "//div[@class='tab_bar']/div[2]"
    active_tab_class = "(//div[@class='tab_item active'])[1]"

    # 筛选器选择器（使用准确的选择器）
    price_filter_selector = "(//div[@class='filter-tab-header__row'])[1]"  # 价格筛选器
    category_filter_selector = "(//div[@class='filter-tab-header__row'])[2]"  # 分类筛选器

    car_card_selector = "//div[@class='series_card']"  # 准确的车卡选择器
    tang_item = "//div[@class='item_content']"  # 糖豆
    old_car_section = "//div[@class='old_car_card']"  # 二手车卡
    shop_banner = "(//div[@class='shop-banner'])[1]"  # 自营门店

    buy_new_car = "(//div[@class='van-grid-item home-tangdou'])[1]"  # 买新车

    shop_product_path = "api/gateway/carmall/pageShopProduct"
    old_car_list = "/api/gateway/carmall/usedcar/list/page"

    price_8 = "//span[contains(text(),'8万以下')]"
    price_15 = "//span[contains(text(),'8-15万')]"
    level_轿车 = "//span[contains(text(),'轿车')]"
    level_SUV = "//span[contains(text(),'SUV')]"
    level_MPV = "//span[contains(text(),'MPV')]"
    type_汽油 = "//span[contains(text(),'汽油车')]"
    type_纯电动 = "//span[contains(text(),'纯电动')]"
    type_插电混动 = "//span[contains(text(),'插电混动')]"
    type_增程式 = "//span[contains(text(),'增程式')]"

    def test_mall_page_basic_load(self, page: Page):
        """测试车商城页面基础加载"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")

        # 验证页面成功加载
        assert page.url.startswith(self.base_url), "页面URL不正确"

        # 验证页面标题存在且不为空
        title = page.title()
        assert title and len(title.strip()) > 0, "页面标题为空"

    def test_brand_section_functionality(self, page: Page):
        """测试品牌区域功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证品牌区域存在
        brand_sections = page.locator(self.brand_section_selector)
        if brand_sections.count() > 0:
            # 验证品牌列表不为空
            brand_items = page.locator(self.brand_list_selector)
            if brand_items.count() > 0:
                print(f"找到品牌列表项: {brand_items.count()}个")
            else:
                print("未找到专门的品牌列表，但品牌区域存在")
        else:
            # 如果没有专门的品牌区域，检查是否有品牌相关的可点击元素
            brand_related = page.locator("text=/品牌|Brand/i")
            print(f"找到品牌相关元素数量: {brand_related.count()}")

    def test_new_used_car_toggle(self, page: Page):
        """测试新车二手车切换功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 查找新车标签
        expect(page.locator(self.active_tab_class)).has_text("买新车")
        page.click(self.old_car_tab_selector)
        expect(page.locator(self.active_tab_class)).has_text("二手车")

    def test_price_filter_functionality(self, page: Page):
        """测试价格筛选功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)
        import json
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.price_8)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("maxPrice") == 80000, "价格筛选参数传递不正确"
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.price_15)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("minPrice") == 150000, "价格筛选参数传递不正确"

    def test_category_filter_functionality(self, page: Page):
        """测试车型分类筛选功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        import json
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.type_汽油)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("type") == [1], "汽油车分类筛选参数传递不正确"
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.type_纯电动)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("type") == [1, 4], "纯电动分类筛选参数传递不正确"
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.type_插电混动)
            page.click(self.type_增程式)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("type") == [1, 4, 5, 6], "插电混动和增程式分类筛选参数传递不正确"

    def test_level_filter_functionality(self, page: Page):
        """测试车型分类筛选功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)
        import json
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.level_轿车)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("level") == [98], "轿车分类筛选参数传递不正确"
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.level_SUV)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("level") == [98, 9], "SUV分类筛选参数传递不正确"
        with page.expect_request(self.shop_product_path) as req_info:
            page.click(self.level_MPV)
        request = req_info.value
        assert json.loads(request.post_data_json()).get("level") == [98, 9, 8], "MPV分类筛选参数传递不正确"

    def test_car_list_display(self, page: Page):
        """测试车辆列表展示功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证车辆列表不为空（优先使用准确的车卡选择器）
        car_items = page.locator(self.car_card_selector)

        assert car_items.count() > 0, "车辆列表为空"

    def test_page_basic_elements_load(self, page: Page):
        """测试页面基础元素加载"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证页面基础结构
        body = page.locator("body")
        assert body.count() > 0, "页面body元素未加载"

        # 验证页面有内容
        page_text = page.text_content("body")
        assert page_text and len(page_text.strip()) > 0, "页面内容为空"

        # 测试页面滚动功能
        page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
        page.wait_for_timeout(500)
        page.evaluate("window.scrollTo(0, 0)")
        page.wait_for_timeout(500)

        # 验证页面仍然正常
        assert page.url.startswith(self.base_url), "页面滚动后URL异常"



    def test_information_display_correctness(self, page: Page):
        """测试信息展示正确性"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证页面信息展示完整性
        # 1. 检查是否有图片加载
        images = page.locator("img")
        if images.count() > 0:
            # 检查前几张图片是否正常加载
            for i in range(min(3, images.count())):
                img = images.nth(i)
                if img.is_visible():
                    src = img.get_attribute("src")
                    assert src and len(src) > 0, f"第{i + 1}张图片src为空"

        # 2. 检查文本信息展示
        text_elements = page.locator("h1, h2, h3, h4, h5, h6, p, span, div")
        text_count = 0
        for i in range(min(10, text_elements.count())):
            element = text_elements.nth(i)
            if element.is_visible():
                text = element.text_content()
                if text and len(text.strip()) > 0:
                    text_count += 1

        assert text_count > 0, "页面缺少有效的文本信息展示"

        # 3. 验证页面布局结构合理
        main_containers = page.locator("main, .main, #main, .container, .content")
        if main_containers.count() == 0:
            # 如果没有明确的主容器，检查是否有基本的div结构
            divs = page.locator("div")
            assert divs.count() > 0, "页面缺少基本的布局结构"
