import pytest
from playwright.sync_api import Page, expect


@pytest.fixture(scope="function")
def browser_context_args(browser_context_args, playwright):
    """配置iPhone 12 Pro设备模拟"""
    iphone_12 = playwright.devices['iPhone 12 Pro']
    return {
        **browser_context_args,
        **iphone_12,
    }


class TestMall:
    """车商城页面通用功能测试"""

    # 页面URL和基本元素选择器
    base_url = "https://energyspace.m.autohome.com.cn/ahoh-marketing/mall"
    query_params = "cityid=810100"

    brand_section_selector = "//div[@class='brand_list']"  # 品牌区域
    brand_list_selector = "//div[@class='brand_item']"  # 品牌列表

    # 车型分类选择器（优先使用稳定属性）
    new_car_tab_selector = "//div[@class='tab_bar']/div[1]"
    old_car_tab_selector = "//div[@class='tab_bar']/div[2]"
    active_tab_class = "(//div[@class='tab_item active'])[1]"

    # 筛选器选择器（使用准确的选择器）
    price_filter_selector = "(//div[@class='filter-tab-header__row'])[1]"  # 价格筛选器
    category_filter_selector = "(//div[@class='filter-tab-header__row'])[2]"  # 分类筛选器

    car_card_selector = "//div[@class='series_card']"  # 准确的车卡选择器
    tang_item = "//div[@class='item_content']"  # 糖豆
    old_car_section = "//div[@class='old_car_card']"  # 二手车卡
    shop_banner = "(//div[@class='shop-banner'])[1]"  # 自营门店

    buy_new_car = "(//div[@class='van-grid-item home-tangdou'])[1]"  # 买新车

    shop_product_path = "api/gateway/carmall/pageShopProduct"
    old_car_list = "/api/gateway/carmall/usedcar/list/page"

    price_8 = "//span[contains(text(),'8万以下')]"
    price_15 = "//span[contains(text(),'8-15万')]"
    level_轿车 = "//span[contains(text(),'轿车')]"
    level_SUV = "//span[contains(text(),'SUV')]"
    level_MPV = "//span[contains(text(),'MPV')]"
    type_汽油 = "//span[contains(text(),'汽油车')]"
    type_纯电动 = "//span[contains(text(),'纯电动')]"
    type_插电混动 = "//span[contains(text(),'插电混动')]"
    type_增程式 = "//span[contains(text(),'增程式')]"

    def test_mall_page_basic_load(self, page: Page):
        """测试车商城页面基础加载"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")

        # 验证页面成功加载
        assert page.url.startswith(self.base_url), "页面URL不正确"

        # 验证页面标题存在且不为空
        title = page.title()
        assert title and len(title.strip()) > 0, "页面标题为空"

    def test_brand_section_functionality(self, page: Page):
        """测试品牌区域功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证品牌区域存在
        brand_sections = page.locator(self.brand_section_selector)
        if brand_sections.count() > 0:
            # 验证品牌列表不为空
            brand_items = page.locator(self.brand_list_selector)
            if brand_items.count() > 0:
                print(f"找到品牌列表项: {brand_items.count()}个")
            else:
                print("未找到专门的品牌列表，但品牌区域存在")
        else:
            # 如果没有专门的品牌区域，检查是否有品牌相关的可点击元素
            brand_related = page.locator("text=/品牌|Brand/i")
            print(f"找到品牌相关元素数量: {brand_related.count()}")

    def test_new_used_car_toggle(self, page: Page):
        """测试新车二手车切换功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 查找新车标签
        expect(page.locator(self.active_tab_class)).to_have_text("买新车")
        page.click(self.old_car_tab_selector)
        expect(page.locator(self.active_tab_class)).to_have_text("二手车")

    def test_price_filter_functionality(self, page: Page):
        """测试价格筛选功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 检查价格筛选元素是否存在
        price_8_element = page.locator(self.price_8)
        price_15_element = page.locator(self.price_15)

        if price_8_element.count() > 0:
            print("找到8万以下价格筛选选项")
            # 尝试点击并检查是否有网络请求
            try:
                import json
                with page.expect_request(lambda req: self.shop_product_path in req.url, timeout=5000) as req_info:
                    price_8_element.click()
                request = req_info.value
                if request.post_data:
                    data = json.loads(request.post_data)
                    print(f"价格筛选请求数据: {data}")
                    # 验证价格参数（根据实际API调整）
                    assert "maxPrice" in data or "price" in data, "价格筛选参数未找到"
            except Exception as e:
                print(f"价格筛选测试异常: {e}")
                # 至少验证元素可以点击
                price_8_element.click()
                page.wait_for_timeout(1000)
        else:
            print("未找到价格筛选选项，跳过此测试")

        if price_15_element.count() > 0:
            print("找到8-15万价格筛选选项")
            try:
                import json
                with page.expect_request(lambda req: self.shop_product_path in req.url, timeout=5000) as req_info:
                    price_15_element.click()
                request = req_info.value
                if request.post_data:
                    data = json.loads(request.post_data)
                    print(f"价格筛选请求数据: {data}")
            except Exception as e:
                print(f"价格筛选测试异常: {e}")
                price_15_element.click()
                page.wait_for_timeout(1000)

    def test_category_filter_functionality(self, page: Page):
        """测试车型分类筛选功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 检查车型分类筛选元素是否存在
        type_elements = [
            (self.type_汽油, "汽油车"),
            (self.type_纯电动, "纯电动"),
            (self.type_插电混动, "插电混动"),
            (self.type_增程式, "增程式")
        ]

        for selector, name in type_elements:
            element = page.locator(selector)
            if element.count() > 0:
                print(f"找到{name}筛选选项")
                try:
                    import json
                    with page.expect_request(lambda req: self.shop_product_path in req.url, timeout=5000) as req_info:
                        element.click()
                    request = req_info.value
                    if request.post_data:
                        data = json.loads(request.post_data)
                        print(f"{name}筛选请求数据: {data}")
                        # 验证类型参数存在
                        assert "fuelType" in data or "type" in data or "category" in data, f"{name}分类筛选参数未找到"
                except Exception as e:
                    print(f"{name}筛选测试异常: {e}")
                    # 至少验证元素可以点击
                    element.click()
                    page.wait_for_timeout(1000)
            else:
                print(f"未找到{name}筛选选项")

    def test_level_filter_functionality(self, page: Page):
        """测试车型级别筛选功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 检查车型级别筛选元素是否存在
        level_elements = [
            (self.level_轿车, "轿车"),
            (self.level_SUV, "SUV"),
            (self.level_MPV, "MPV")
        ]

        for selector, name in level_elements:
            element = page.locator(selector)
            if element.count() > 0:
                print(f"找到{name}筛选选项")
                try:
                    import json
                    with page.expect_request(lambda req: self.shop_product_path in req.url, timeout=5000) as req_info:
                        element.click()
                    request = req_info.value
                    if request.post_data:
                        data = json.loads(request.post_data)
                        print(f"{name}筛选请求数据: {data}")
                        # 验证级别参数存在
                        assert "level" in data or "category" in data, f"{name}级别筛选参数未找到"
                except Exception as e:
                    print(f"{name}筛选测试异常: {e}")
                    # 至少验证元素可以点击
                    element.click()
                    page.wait_for_timeout(1000)
            else:
                print(f"未找到{name}筛选选项")

    def test_car_list_display(self, page: Page):
        """测试车辆列表展示功能"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证车辆列表不为空（优先使用准确的车卡选择器）
        car_items = page.locator(self.car_card_selector)

        assert car_items.count() > 0, "车辆列表为空"

    def test_page_basic_elements_load(self, page: Page):
        """测试页面基础元素加载"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证页面基础结构
        body = page.locator("body")
        assert body.count() > 0, "页面body元素未加载"

        # 验证页面有内容
        page_text = page.text_content("body")
        assert page_text and len(page_text.strip()) > 0, "页面内容为空"

        # 测试页面滚动功能
        page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
        page.wait_for_timeout(500)
        page.evaluate("window.scrollTo(0, 0)")
        page.wait_for_timeout(500)

        # 验证页面仍然正常
        assert page.url.startswith(self.base_url), "页面滚动后URL异常"



    def test_information_display_correctness(self, page: Page):
        """测试信息展示正确性"""
        # 访问页面
        page.goto(f"{self.base_url}?{self.query_params}")
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)

        # 验证页面信息展示完整性
        # 1. 检查是否有图片加载
        images = page.locator("img")
        if images.count() > 0:
            # 检查前几张图片是否正常加载
            for i in range(min(3, images.count())):
                img = images.nth(i)
                if img.is_visible():
                    src = img.get_attribute("src")
                    assert src and len(src) > 0, f"第{i + 1}张图片src为空"

        # 2. 检查文本信息展示
        text_elements = page.locator("h1, h2, h3, h4, h5, h6, p, span, div")
        text_count = 0
        for i in range(min(10, text_elements.count())):
            element = text_elements.nth(i)
            if element.is_visible():
                text = element.text_content()
                if text and len(text.strip()) > 0:
                    text_count += 1

        assert text_count > 0, "页面缺少有效的文本信息展示"

        # 3. 验证页面布局结构合理
        main_containers = page.locator("main, .main, #main, .container, .content")
        if main_containers.count() == 0:
            # 如果没有明确的主容器，检查是否有基本的div结构
            divs = page.locator("div")
            assert divs.count() > 0, "页面缺少基本的布局结构"
