import pytest
from playwright.sync_api import Page, expect


@pytest.fixture(scope="function")
def browser_context_args(browser_context_args,playwright):
    iphone_12 = playwright.devices['iPhone 12 Pro']
    return {
        **browser_context_args,
        **iphone_12,
    }

class TestBoce:
    """帮买车页面测试"""

    series_card = "//div[@class='index4-series-card']"
    base_url = "https://energyspace.m.autohome.com.cn/ahoh-marketing/ahohIndex4"
    store_name = "(//div[@class='name'])[1]"
    store_sales_cont_name = "//div[@class='store-sales-cont-name']"

    def test_boce_page_elements(self, page: Page):
        """测试帮买车页面元素"""
        # 访问页面
        page.goto(f"{self.base_url}?rimCityId=110100&storeCode=3425")
        page.wait_for_load_state("networkidle")

        # 验证页面标题
        expect(page).to_have_title("帮买车")
        
        # 验证车系卡片存在
        assert page.locator(self.series_card).count() > 0, "未找到车系卡片"

        # 验证空间站名称
        store_name_text = page.locator(self.store_name).text_content().strip()
        assert store_name_text == "汽车之家空间站·北京站", f"{store_name_text}空间站名称不正确"

        # 验证金牌销售名称
        sales_name_text = page.locator(self.store_sales_cont_name).text_content().strip()
        assert sales_name_text == "静静管家", f"{sales_name_text}金牌销售名称不正确"
