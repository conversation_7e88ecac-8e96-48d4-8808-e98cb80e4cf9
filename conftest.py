import pytest
from playwright.sync_api import sync_playwright, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>onte<PERSON><PERSON>, Page


@pytest.fixture(scope="session")
def playwright():
    """启动Playwright"""
    with sync_playwright() as p:
        yield p


@pytest.fixture(scope="session")
def browser_type_launch_args():
    """配置浏览器启动参数"""
    return {
        "headless": <PERSON>als<PERSON>,  # 启用有头模式
        "slow_mo": 500,    # 减慢操作速度，便于观察
    }


@pytest.fixture(scope="session")
def browser(playwright: Playwright, browser_type_launch_args):
    """启动浏览器"""
    browser = playwright.chromium.launch(**browser_type_launch_args)
    yield browser
    browser.close()


@pytest.fixture(scope="function")
def browser_context_args(playwright: Playwright):
    """配置浏览器上下文参数，启用iPhone 12 Pro设备模拟"""
    iphone_12 = playwright.devices['iPhone 12 Pro']
    return {
        **iphone_12,
    }


@pytest.fixture(scope="function")
def context(browser: <PERSON><PERSON><PERSON>, browser_context_args):
    """创建浏览器上下文"""
    context = browser.new_context(**browser_context_args)
    yield context
    context.close()


@pytest.fixture(scope="function")
def page(context: BrowserContext):
    """创建页面"""
    page = context.new_page()
    yield page
    page.close()